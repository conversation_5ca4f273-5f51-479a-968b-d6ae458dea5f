#!/usr/bin/env python3
"""
Playwright自动化测试脚本
测试最近修复的三个核心功能：
1. 单个保存按钮功能
2. 废弃按钮文本显示问题  
3. SQL模板加载体验
"""

import asyncio
import sys
import time
from playwright.async_api import async_playwright, expect

class DataDictionaryTester:
    def __init__(self):
        self.base_url = "http://localhost:8000"
        self.browser = None
        self.context = None
        self.page = None
        
    async def setup(self):
        """初始化浏览器和页面"""
        playwright = await async_playwright().start()
        self.browser = await playwright.chromium.launch(headless=False, slow_mo=1000)
        self.context = await self.browser.new_context(
            viewport={'width': 1920, 'height': 1080}
        )
        self.page = await self.context.new_page()
        
        # 启用控制台日志监听
        self.page.on("console", lambda msg: print(f"🖥️ 控制台: {msg.text}"))
        self.page.on("pageerror", lambda error: print(f"❌ 页面错误: {error}"))
        
    async def cleanup(self):
        """清理资源"""
        if self.browser:
            await self.browser.close()
            
    async def test_table_detail_page_loading(self):
        """测试表详情页面基础加载功能"""
        print("\n🔍 测试1: 表详情页面基础加载")
        
        try:
            # 导航到表详情页面（假设存在一个测试表）
            await self.page.goto(f"{self.base_url}/public/data_dictionary/")
            await self.page.wait_for_load_state('networkidle')
            
            print("✅ 页面加载成功")
            
            # 检查页面是否包含关键元素
            page_title = await self.page.title()
            print(f"📄 页面标题: {page_title}")
            
            return True
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            return False
            
    async def test_deprecated_button_functionality(self):
        """测试废弃按钮功能和文本显示"""
        print("\n🔍 测试2: 废弃按钮功能测试")
        
        try:
            # 查找废弃按钮
            deprecated_button = self.page.locator('#toggleTableStatusBtn')
            
            if await deprecated_button.count() > 0:
                print("✅ 找到废弃按钮")
                
                # 获取按钮当前文本
                button_text = await deprecated_button.text_content()
                print(f"🔍 按钮当前文本: {button_text}")
                
                # 点击按钮
                print("🖱️ 点击废弃按钮...")
                await deprecated_button.click()
                
                # 等待AJAX请求完成
                await self.page.wait_for_timeout(3000)
                
                # 检查按钮文本是否更新
                new_button_text = await deprecated_button.text_content()
                print(f"🔍 按钮更新后文本: {new_button_text}")
                
                if button_text != new_button_text:
                    print("✅ 按钮文本已更新")
                    return True
                else:
                    print("⚠️ 按钮文本未更新，可能需要检查")
                    return False
                    
            else:
                print("❌ 未找到废弃按钮")
                return False
                
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            return False
            
    async def test_sql_template_loading(self):
        """测试SQL模板加载体验"""
        print("\n🔍 测试3: SQL模板加载体验测试")
        
        try:
            # 查找SQL模板标签页按钮
            sql_tab_button = self.page.locator('button[data-bs-target="#sql-templates"]')
            
            if await sql_tab_button.count() > 0:
                print("✅ 找到SQL模板标签页按钮")
                
                # 点击SQL模板标签页
                print("🖱️ 点击SQL模板标签页...")
                await sql_tab_button.click()
                
                # 等待加载状态出现
                await self.page.wait_for_timeout(1000)
                
                # 检查是否显示加载指示器
                loading_indicator = self.page.locator('.spinner-border')
                if await loading_indicator.count() > 0:
                    print("✅ 显示了加载指示器")
                    
                    # 等待加载完成
                    await self.page.wait_for_timeout(5000)
                    
                    # 检查内容是否加载
                    sql_content = self.page.locator('#sql-templates-content')
                    content_text = await sql_content.text_content()
                    
                    if content_text and "正在加载" not in content_text:
                        print("✅ SQL模板内容加载完成")
                        return True
                    else:
                        print("⚠️ SQL模板内容可能未完全加载")
                        return False
                else:
                    print("⚠️ 未显示加载指示器")
                    return False
                    
            else:
                print("❌ 未找到SQL模板标签页按钮")
                return False
                
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            return False
            
    async def test_data_supplement_save_button(self):
        """测试数据补充页面的单个保存按钮功能"""
        print("\n🔍 测试4: 数据补充页面单个保存按钮测试")
        
        try:
            # 导航到数据补充页面
            await self.page.goto(f"{self.base_url}/public/data_dictionary/data_supplement/")
            await self.page.wait_for_load_state('networkidle')
            
            print("✅ 数据补充页面加载成功")
            
            # 查找单个保存按钮
            save_buttons = self.page.locator('button[data-action="save-table"]')
            button_count = await save_buttons.count()
            
            if button_count > 0:
                print(f"✅ 找到 {button_count} 个单个保存按钮")
                
                # 测试第一个保存按钮
                first_button = save_buttons.first
                
                # 检查按钮是否可点击
                is_enabled = await first_button.is_enabled()
                print(f"🔍 按钮是否可点击: {is_enabled}")
                
                if is_enabled:
                    print("🖱️ 点击保存按钮...")
                    await first_button.click()
                    
                    # 等待响应
                    await self.page.wait_for_timeout(2000)
                    
                    print("✅ 保存按钮点击成功")
                    return True
                else:
                    print("⚠️ 保存按钮不可点击")
                    return False
                    
            else:
                print("❌ 未找到单个保存按钮")
                return False
                
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            return False
            
    async def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始自动化测试...")
        
        await self.setup()
        
        test_results = []
        
        # 运行所有测试
        tests = [
            ("页面基础加载", self.test_table_detail_page_loading),
            ("废弃按钮功能", self.test_deprecated_button_functionality),
            ("SQL模板加载", self.test_sql_template_loading),
            ("单个保存按钮", self.test_data_supplement_save_button),
        ]
        
        for test_name, test_func in tests:
            try:
                result = await test_func()
                test_results.append((test_name, result))
            except Exception as e:
                print(f"❌ 测试 {test_name} 执行异常: {e}")
                test_results.append((test_name, False))
                
        # 输出测试结果汇总
        print("\n" + "="*50)
        print("📊 测试结果汇总:")
        print("="*50)
        
        passed = 0
        total = len(test_results)
        
        for test_name, result in test_results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{test_name}: {status}")
            if result:
                passed += 1
                
        print(f"\n总计: {passed}/{total} 个测试通过")
        
        await self.cleanup()
        
        return passed == total

async def main():
    """主函数"""
    tester = DataDictionaryTester()
    success = await tester.run_all_tests()
    
    if success:
        print("\n🎉 所有测试通过！")
        sys.exit(0)
    else:
        print("\n⚠️ 部分测试失败，请检查修复情况")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
